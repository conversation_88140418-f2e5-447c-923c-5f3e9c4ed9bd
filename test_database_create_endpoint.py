#!/usr/bin/env python3
"""
Standalone test script for testing the POST /web/database/create endpoint
with master password 'ttl1034'
"""

import requests
import sys
import time
import subprocess
import os
from urllib.parse import urljoin


class DatabaseCreateTest:
    def __init__(self, base_url="http://localhost:8069", master_password="ttl1034"):
        self.base_url = base_url
        self.master_password = master_password
        self.session = requests.Session()
        
    def test_database_creation(self):
        """Test database creation via POST /web/database/create"""
        print(f"Testing database creation with master password: {self.master_password}")
        
        # Generate unique database name
        test_db_name = f"test_db_create_{int(time.time())}"
        
        try:
            # First, get the database manager page to establish session
            manager_url = urljoin(self.base_url, '/web/database/manager')
            print(f"Getting database manager page: {manager_url}")
            
            manager_response = self.session.get(manager_url)
            if manager_response.status_code != 200:
                print(f"Failed to access database manager: {manager_response.status_code}")
                return False
                
            print("Successfully accessed database manager page")
            
            # Prepare data for database creation
            create_data = {
                'master_pwd': self.master_password,
                'name': test_db_name,
                'login': 'admin',
                'password': 'admin',
                'lang': 'en_US',
                'phone': '',
                'demo': False,  # No demo data
                'country_code': False
            }
            
            # Make POST request to create database
            create_url = urljoin(self.base_url, '/web/database/create')
            print(f"Creating database '{test_db_name}' at: {create_url}")
            print(f"Request data: {create_data}")
            
            create_response = self.session.post(
                create_url, 
                data=create_data, 
                allow_redirects=False
            )
            
            print(f"Response status code: {create_response.status_code}")
            print(f"Response headers: {dict(create_response.headers)}")
            
            # Check for successful creation (should redirect to /odoo)
            if create_response.status_code == 303:
                location = create_response.headers.get('Location', '')
                if '/odoo' in location:
                    print(f"✅ SUCCESS: Database '{test_db_name}' created successfully!")
                    print(f"Redirected to: {location}")
                    
                    # Try to clean up the created database
                    self.cleanup_database(test_db_name)
                    return True
                else:
                    print(f"❌ UNEXPECTED: Got redirect but not to /odoo: {location}")
                    return False
            else:
                print(f"❌ FAILED: Expected status 303, got {create_response.status_code}")
                if create_response.text:
                    print(f"Response body: {create_response.text[:500]}...")
                return False
                
        except requests.exceptions.ConnectionError as e:
            print(f"❌ CONNECTION ERROR: Could not connect to {self.base_url}")
            print(f"Error: {e}")
            print("Make sure Odoo server is running on the specified URL")
            return False
        except Exception as e:
            print(f"❌ ERROR: {e}")
            return False
    
    def cleanup_database(self, db_name):
        """Attempt to clean up the created database"""
        try:
            print(f"Attempting to clean up database: {db_name}")
            drop_url = urljoin(self.base_url, '/web/database/drop')
            drop_data = {
                'master_pwd': self.master_password,
                'name': db_name
            }
            
            drop_response = self.session.post(
                drop_url,
                data=drop_data,
                allow_redirects=False
            )
            
            if drop_response.status_code == 303:
                print(f"✅ Database '{db_name}' cleaned up successfully")
            else:
                print(f"⚠️  Could not clean up database '{db_name}' (status: {drop_response.status_code})")
                print("You may need to manually drop this database")
                
        except Exception as e:
            print(f"⚠️  Error during cleanup: {e}")
            print("You may need to manually drop the test database")


def check_odoo_server(base_url="http://localhost:8069"):
    """Check if Odoo server is running"""
    try:
        response = requests.get(base_url, timeout=5)
        return response.status_code == 200
    except:
        return False


def start_odoo_server():
    """Attempt to start Odoo server if not running"""
    print("Odoo server not detected. Attempting to start...")
    
    # Check if odoo_runner.py exists (based on memories)
    if os.path.exists('odoo_runner.py'):
        print("Starting Odoo using odoo_runner.py...")
        process = subprocess.Popen(['python', 'odoo_runner.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        time.sleep(10)  # Give it time to start
        return process
    else:
        print("Starting Odoo using odoo-bin...")
        process = subprocess.Popen(['python', 'odoo-bin', '--db-filter=.*'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        time.sleep(10)  # Give it time to start
        return process


def main():
    base_url = "http://localhost:8069"
    master_password = "ttl1034"

    print("=" * 60)
    print("DATABASE CREATE ENDPOINT TEST")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Master Password: {master_password}")
    print()
    print("INSTRUCTIONS:")
    print("1. Make sure Odoo server is running with --list-db option enabled")
    print("2. Ensure the master password is set to 'ttl1034' or admin password verification is disabled")
    print("3. Run: python odoo-bin --db-filter=.* --list-db")
    print()

    # Check if server is running
    if not check_odoo_server(base_url):
        print("❌ Odoo server is not running or not accessible.")
        print()
        print("To start Odoo server manually:")
        print("  python odoo-bin --db-filter=.* --list-db")
        print("  OR")
        print("  python odoo_runner.py  (then type 's' to start)")
        print()
        print("Once the server is running, run this test again.")
        return 1
    else:
        print("✅ Odoo server is running")

    # Run the test
    print()
    tester = DatabaseCreateTest(base_url, master_password)
    success = tester.test_database_creation()

    print()
    print("=" * 60)
    if success:
        print("✅ TEST PASSED: Database creation endpoint works correctly!")
        print("The POST /web/database/create endpoint successfully creates databases")
        print("with master password 'ttl1034' without any errors.")
    else:
        print("❌ TEST FAILED: Database creation endpoint has issues")
        print("Check the error messages above for details.")
    print("=" * 60)

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
